# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

# project specific
doorbit_esg.json
new-flow.json
.clinerules
.clineignore

npm-debug.log*
yarn-debug.log*
yarn-error.log*
doorbit_original.json