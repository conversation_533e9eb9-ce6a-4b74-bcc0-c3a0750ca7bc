{"name": "formular-schema-editor", "version": "0.1.0", "private": true, "homepage": "https://doorbit.github.io/flow-ui-toolkit", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mdi/js": "^7.4.47", "@mdi/react": "^1.6.1", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.2", "@types/styled-components": "^5.1.34", "ajv": "^8.17.1", "react": "^19.1.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.0", "react-json-view": "^1.21.3", "react-scripts": "5.0.1", "styled-components": "^6.1.17", "typescript": "^4.9.5", "uuid": "^11.1.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject", "predeploy": "npm run build", "deploy": "gh-pages -d build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "jest": {"transformIgnorePatterns": ["node_modules/(?!(react-dnd|react-dnd-html5-backend|dnd-core|@react-dnd|@mui)/)"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/index.tsx", "!src/reportWebVitals.ts"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^7.1.0", "@types/lodash": "^4.17.16", "@types/uuid": "^10.0.0", "gh-pages": "^6.3.0"}}