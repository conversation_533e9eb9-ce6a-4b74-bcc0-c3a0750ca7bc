# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and deploy to GitHub Pages
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: Build and Deploy

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]
  workflow_dispatch:

permissions:
  contents: write

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
    - name: Checkout 🛎️
      uses: actions/checkout@v4
    - name: Use Node.js ${{ matrix.node-version }} 🔧
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    - name: Install dependencies 📦
      run: npm ci --legacy-peer-deps
    - name: Build 🏗️
      run: npm run build --if-present
    - name: Test 🧪
      run: npm test
    - name: Upload build artifacts 📤
      uses: actions/upload-artifact@v4
      with:
        name: build-files
        path: build/

  deploy:
    needs: build-and-test
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request'

    steps:
    - name: Checkout 🛎️
      uses: actions/checkout@v4
    - name: Download build artifacts 📥
      uses: actions/download-artifact@v4
      with:
        name: build-files
        path: build
    - name: Deploy to GitHub Pages 🚀
      uses: JamesIves/github-pages-deploy-action@v4.7.3
      with:
        branch: gh-pages
        folder: build
        clean: true
